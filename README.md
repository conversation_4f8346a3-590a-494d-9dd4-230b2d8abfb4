# AI Utility Orchestrator

AI Utility Orchestrator is a universal, AI-powered orchestration framework that dynamically routes user queries to the most appropriate tool or agent using Large Language Models (LLMs), a pluggable registry, and context-aware session management.

## Features

- LLM-powered dynamic tool/agent selection
- Pluggable agent and tool registry (no hardcoding)
- Context management across sessions
- Modular response formatting
- Domain-agnostic and reusable across projects
- Clean and extensible architecture

## Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/your-org/ai_utility_orchestrator.git
cd ai_utility_orchestrator

# Install dependencies
pip install -r requirements.txt

# Or install as a package
pip install .
