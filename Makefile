# Makefile for AI Utility Orchestrator

.PHONY: help install install-dev test test-unit test-integration test-coverage clean lint format build

# Default target
help:
	@echo "AI Utility Orchestrator - Available Commands:"
	@echo ""
	@echo "Setup:"
	@echo "  install      - Install package dependencies"
	@echo "  install-dev  - Install package with development dependencies"
	@echo ""
	@echo "Testing:"
	@echo "  test         - Run all tests"
	@echo "  test-unit    - Run unit tests only"
	@echo "  test-integration - Run integration tests only"
	@echo "  test-coverage - Run tests with coverage report"
	@echo "  test-fast    - Run fast tests only"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint         - Run linting checks"
	@echo "  format       - Format code with black"
	@echo ""
	@echo "Build:"
	@echo "  build        - Build package wheel and source distribution"
	@echo "  clean        - Clean build artifacts"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"

# Testing
test:
	python run_tests.py all

test-unit:
	python run_tests.py unit

test-integration:
	python run_tests.py integration

test-coverage:
	python run_tests.py coverage --html-cov

test-fast:
	python run_tests.py fast

# Code quality
lint:
	@echo "Running mypy type checking..."
	mypy core utils --ignore-missing-imports
	@echo "✅ Linting completed"

format:
	@echo "Formatting code with black..."
	black core utils tests *.py
	@echo "✅ Code formatting completed"

# Build
build: clean
	@echo "Building package..."
	python -m build
	@echo "✅ Package built successfully"
	@echo "Files created:"
	@ls -la dist/

clean:
	@echo "Cleaning build artifacts..."
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	@echo "✅ Cleanup completed"

# Development workflow
dev-setup: install-dev
	@echo "Development environment setup complete!"
	@echo "Run 'make test' to verify everything works."

# Quick development test
dev-test: test-fast lint
	@echo "✅ Quick development checks passed!"
