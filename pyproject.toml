[project]
name = "ai_utility_orchestrator"
version = "0.1.0"
description = "A universal, AI-powered utility orchestration package with dynamic LLM-driven routing."
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
  { name = "<PERSON><PERSON><PERSON>" }
]

dependencies = [
  "openai",
  "python-dotenv",
  "PyYAML"
]

[project.optional-dependencies]
dev = [
  "pytest",
  "black",
  "mypy"
]

[tool.setuptools]
packages = ["ai_utility_orchestrator"]

[tool.setuptools.package-dir]
"ai_utility_orchestrator" = "."

[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"
