{"llm": {"model": "gpt-4o-mini", "temperature": 0.5, "max_tokens": 800}, "system_prompt": "You are an AI Orchestrator that helps users by routing queries to appropriate tools.", "default_user_id": "default_user", "context_limit": 3, "enable_parameter_enhancement": false, "enable_ai_response_generation": false, "context_storage": {"backend": "file", "file_path": "data/chat_history.json"}, "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": false}, "registry_config": {"registration_message": "Registered tool: {tool_name}"}, "parameter_enhancement": {"temperature": 0.3}, "json_extraction": {"patterns": ["\\{.*?\\}", "```json\\s*(\\{.*?\\})\\s*```", "\\{[^{}]*\"selected_tool\"[^{}]*\\}"]}, "log_messages": {"start": " Starting agent_executor...", "tool_registered": " Registered tool: {tool_name}", "tool_import_failed": " Could not import {tool_name}: {error}", "tool_selection": " Asking LLM to select tool...", "tool_execution": "Executing tool: {tool_name}", "direct_response": " Providing direct response"}, "prompt_templates": {"tool_selection": "You are an AI Orchestrator. Analyze the user's request and determine which tool to use.\n\nAvailable tools:\n{tools_text}\n\nUser request: {user_input}{context_text}\n\nIMPORTANT: You MUST respond with ONLY a valid JSON object. No additional text.\n\nRequired JSON format:\n{{\"selected_tool\": \"tool_name_or_none\", \"parameters\": {{}}, \"reasoning\": \"brief_explanation\", \"direct_response\": \"answer_if_no_tool_needed\"}}\n\nRespond with JSON only:", "final_response": "Based on the tool execution result, provide a helpful response to the user.\n\nUser's original question: {user_input}\nTool used: {selected_tool_name}\nTool result: {tool_result}\n\nPlease provide a natural, helpful response to the user based on this information."}, "error_messages": {"tool_execution_failed": "I tried to use the {tool_name} tool, but encountered an error: {error}", "tool_not_found": "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}", "general_error": "I apologize, but I encountered an error while processing your request: {error}", "parse_error": "Could not understand how to process your request"}, "default_responses": {"no_tool_needed": "I can help you with that, but I'm not sure how to respond."}, "tools": [{"name": "search_tool", "description": "Search the web for information with configurable options", "execute_func": "core.tools.search_execute", "schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query"}, "search_type": {"type": "string", "enum": ["web", "academic", "news"], "default": "web"}, "max_results": {"type": "integer", "default": 5}, "response_template": {"type": "string", "default": "Search results for: {query}"}}, "required": ["query"]}}, {"name": "registry_tool", "description": "List all available tools with configurable output format", "execute_func": "core.tools.registry_query_tool_execute", "schema": {"type": "object", "properties": {"output_format": {"type": "string", "enum": ["list", "json", "detailed"], "default": "list"}, "include_descriptions": {"type": "boolean", "default": false}, "list_template": {"type": "string", "default": "Available tools: {tools}"}}}}]}