"""
Tests for utils.context_manager module.
"""

import pytest
import json
import tempfile
from pathlib import Path
from utils.context_manager import ContextManager


class TestContextManager:
    """Test the ContextManager class."""
    
    def test_context_manager_creation_default_path(self):
        """Test creating ContextManager with default path."""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Change to temp directory to avoid creating files in project
            original_cwd = Path.cwd()
            try:
                import os
                os.chdir(temp_dir)
                
                cm = ContextManager()
                assert cm.history_path.name == "chat_history.json"
                assert cm.history_path.parent.name == "data"
                assert cm.history == {}
            finally:
                os.chdir(original_cwd)
    
    def test_context_manager_creation_custom_path(self, temp_context_dir):
        """Test creating ContextManager with custom path."""
        history_path = Path(temp_context_dir) / "custom_history.json"
        cm = ContextManager(str(history_path))
        
        assert cm.history_path == history_path
        assert cm.history == {}
    
    def test_add_interaction(self, mock_context_manager):
        """Test adding an interaction."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        
        assert "user1" in cm.history
        assert len(cm.history["user1"]) == 1
        assert cm.history["user1"][0]["user"] == "Hello"
        assert cm.history["user1"][0]["bot"] == "Hi there!"
    
    def test_add_multiple_interactions(self, mock_context_manager):
        """Test adding multiple interactions for the same user."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        cm.add_interaction("user1", "How are you?", "I'm doing well!")
        
        assert len(cm.history["user1"]) == 2
        assert cm.history["user1"][0]["user"] == "Hello"
        assert cm.history["user1"][1]["user"] == "How are you?"
    
    def test_add_interactions_multiple_users(self, mock_context_manager):
        """Test adding interactions for multiple users."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        cm.add_interaction("user2", "Good morning", "Good morning to you too!")
        
        assert "user1" in cm.history
        assert "user2" in cm.history
        assert len(cm.history["user1"]) == 1
        assert len(cm.history["user2"]) == 1
    
    def test_get_history(self, mock_context_manager):
        """Test getting history for a user."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        cm.add_interaction("user1", "How are you?", "I'm doing well!")
        
        history = cm.get_history("user1")
        assert len(history) == 2
        assert history[0]["user"] == "Hello"
        assert history[1]["user"] == "How are you?"
    
    def test_get_history_nonexistent_user(self, mock_context_manager):
        """Test getting history for a user that doesn't exist."""
        cm = mock_context_manager
        
        history = cm.get_history("nonexistent_user")
        assert history == []
    
    def test_get_recent_messages_default(self, mock_context_manager):
        """Test getting recent messages with default settings."""
        cm = mock_context_manager
        
        # Add multiple interactions
        for i in range(10):
            cm.add_interaction("user1", f"Message {i}", f"Response {i}")
        
        recent = cm.get_recent_messages("user1", limit=3)
        
        # Should get 3 interactions = 6 messages (user + assistant for each)
        assert len(recent) == 6
        
        # Check format
        assert recent[0]["role"] == "user"
        assert recent[1]["role"] == "assistant"
        
        # Check it gets the most recent ones
        assert "Message 7" in recent[0]["content"]
        assert "Message 9" in recent[4]["content"]
    
    def test_get_recent_messages_custom_format(self, mock_context_manager):
        """Test getting recent messages with custom format configuration."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        
        format_config = {
            "user_role": "human",
            "assistant_role": "ai",
            "include_metadata": True
        }
        
        recent = cm.get_recent_messages("user1", limit=1, format_config=format_config)
        
        assert len(recent) == 2
        assert recent[0]["role"] == "human"
        assert recent[1]["role"] == "ai"
        assert "metadata" in recent[0]
        assert "metadata" in recent[1]
        assert recent[0]["metadata"]["user_id"] == "user1"
    
    def test_get_recent_messages_limit_larger_than_history(self, mock_context_manager):
        """Test getting recent messages when limit is larger than history."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        
        recent = cm.get_recent_messages("user1", limit=10)
        
        # Should only get the available interactions
        assert len(recent) == 2  # 1 interaction = 2 messages
    
    def test_clear_history_specific_user(self, mock_context_manager):
        """Test clearing history for a specific user."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        cm.add_interaction("user2", "Good morning", "Good morning!")
        
        cm.clear_history("user1")
        
        assert cm.history["user1"] == []
        assert len(cm.history["user2"]) == 1
    
    def test_clear_history_all_users(self, mock_context_manager):
        """Test clearing history for all users."""
        cm = mock_context_manager
        
        cm.add_interaction("user1", "Hello", "Hi there!")
        cm.add_interaction("user2", "Good morning", "Good morning!")
        
        cm.clear_history()
        
        assert cm.history == {}
    
    def test_persistence_save_and_load(self, temp_context_dir):
        """Test that history is saved and loaded correctly."""
        history_path = Path(temp_context_dir) / "test_history.json"
        
        # Create first context manager and add data
        cm1 = ContextManager(str(history_path))
        cm1.add_interaction("user1", "Hello", "Hi there!")
        
        # Create second context manager with same path
        cm2 = ContextManager(str(history_path))
        
        # Should load the saved data
        assert "user1" in cm2.history
        assert len(cm2.history["user1"]) == 1
        assert cm2.history["user1"][0]["user"] == "Hello"
    
    def test_directory_creation(self, temp_context_dir):
        """Test that parent directories are created if they don't exist."""
        nested_path = Path(temp_context_dir) / "nested" / "deep" / "history.json"
        
        cm = ContextManager(str(nested_path))
        cm.add_interaction("user1", "Hello", "Hi there!")
        
        # Directory should be created
        assert nested_path.parent.exists()
        assert nested_path.exists()
    
    def test_load_existing_history_file(self, temp_context_dir):
        """Test loading an existing history file."""
        history_path = Path(temp_context_dir) / "existing_history.json"
        
        # Create existing history file
        existing_data = {
            "user1": [
                {"user": "Existing message", "bot": "Existing response"}
            ]
        }
        
        with open(history_path, 'w') as f:
            json.dump(existing_data, f)
        
        # Load with ContextManager
        cm = ContextManager(str(history_path))
        
        assert "user1" in cm.history
        assert len(cm.history["user1"]) == 1
        assert cm.history["user1"][0]["user"] == "Existing message"
