"""
Tests for utils.response_formatter module.
"""

import pytest
import json
from unittest.mock import Mock, patch
from utils.response_formatter import format_response, _parse_response


class TestParseResponse:
    """Test the _parse_response helper function."""
    
    def test_parse_json_direct(self):
        """Test parsing direct JSON response."""
        json_text = '{"key": "value", "number": 42}'
        result = _parse_response(json_text, "json")
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 42
    
    def test_parse_json_with_code_blocks(self):
        """Test parsing JSON wrapped in code blocks."""
        json_text = '```json\n{"key": "value"}\n```'
        result = _parse_response(json_text, "json")
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
    
    def test_parse_json_embedded(self):
        """Test parsing JSON embedded in other text."""
        json_text = 'Here is the result: {"key": "value"} and that\'s it.'
        result = _parse_response(json_text, "json")
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
    
    def test_parse_json_invalid(self):
        """Test parsing invalid JSON returns raw text."""
        json_text = 'This is not valid JSON'
        result = _parse_response(json_text, "json")
        
        assert result == "This is not valid JSON"
    
    def test_parse_yaml(self):
        """Test parsing YAML response."""
        yaml_text = 'key: value\nnumber: 42'
        result = _parse_response(yaml_text, "yaml")
        
        assert isinstance(result, dict)
        assert result["key"] == "value"
        assert result["number"] == 42
    
    def test_parse_answer(self):
        """Test parsing plain text answer."""
        text = "This is a plain text answer."
        result = _parse_response(text, "answer")
        
        assert result == "This is a plain text answer."
    
    def test_parse_empty_response(self):
        """Test parsing empty response."""
        result = _parse_response("", "json")
        assert result is None
        
        result = _parse_response(None, "json")
        assert result is None
    
    def test_parse_whitespace_only(self):
        """Test parsing whitespace-only response."""
        result = _parse_response("   \n\t  ", "json")
        assert result is None


class TestFormatResponse:
    """Test the format_response function."""
    
    @patch('utils.response_formatter.client')
    def test_format_response_basic(self, mock_client):
        """Test basic format_response functionality."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "This is a test response"
        mock_client.chat.completions.create.return_value = mock_response
        
        result = format_response("Test prompt", formatter="answer")
        
        assert result == "This is a test response"
        mock_client.chat.completions.create.assert_called_once()
    
    @patch('utils.response_formatter.client')
    def test_format_response_json(self, mock_client):
        """Test format_response with JSON formatter."""
        # Mock OpenAI response with JSON
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = '{"result": "success", "data": 123}'
        mock_client.chat.completions.create.return_value = mock_response
        
        result = format_response("Test prompt", formatter="json")
        
        assert isinstance(result, dict)
        assert result["result"] == "success"
        assert result["data"] == 123
    
    @patch('utils.response_formatter.client')
    def test_format_response_with_meta(self, mock_client):
        """Test format_response with return_meta=True."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_client.chat.completions.create.return_value = mock_response
        
        result = format_response("Test prompt", return_meta=True)
        
        assert isinstance(result, dict)
        assert result["raw_response"] == "Test response"
        assert result["parsed_response"] == "Test response"
        assert result["used_model"] == "gpt-4o-mini"
        assert result["error"] is None
    
    @patch('utils.response_formatter.client')
    def test_format_response_with_system_prompt(self, mock_client):
        """Test format_response with system prompt."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_client.chat.completions.create.return_value = mock_response
        
        format_response(
            "Test prompt",
            system_prompt="You are a helpful assistant."
        )
        
        # Check that system prompt was included in messages
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]["messages"]
        
        assert len(messages) == 2
        assert messages[0]["role"] == "system"
        assert messages[0]["content"] == "You are a helpful assistant."
        assert messages[1]["role"] == "user"
        assert messages[1]["content"] == "Test prompt"
    
    @patch('utils.response_formatter.client')
    def test_format_response_with_previous_messages(self, mock_client):
        """Test format_response with previous conversation messages."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_client.chat.completions.create.return_value = mock_response
        
        previous_messages = [
            {"role": "user", "content": "Previous question"},
            {"role": "assistant", "content": "Previous answer"}
        ]
        
        format_response(
            "Current prompt",
            messages=previous_messages
        )
        
        # Check that previous messages were included
        call_args = mock_client.chat.completions.create.call_args
        messages = call_args[1]["messages"]
        
        assert len(messages) == 3
        assert messages[0]["content"] == "Previous question"
        assert messages[1]["content"] == "Previous answer"
        assert messages[2]["content"] == "Current prompt"
    
    @patch('utils.response_formatter.client')
    def test_format_response_custom_model_and_temperature(self, mock_client):
        """Test format_response with custom model and temperature."""
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Response"
        mock_client.chat.completions.create.return_value = mock_response
        
        format_response(
            "Test prompt",
            model_name="gpt-4",
            temperature=0.2
        )
        
        # Check that custom parameters were used
        call_args = mock_client.chat.completions.create.call_args
        assert call_args[1]["model"] == "gpt-4"
        assert call_args[1]["temperature"] == 0.2
    
    @patch('utils.response_formatter.client')
    def test_format_response_api_error(self, mock_client):
        """Test format_response when API call fails."""
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        result = format_response("Test prompt", return_meta=True)
        
        assert isinstance(result, dict)
        assert result["raw_response"] is None
        assert result["parsed_response"] is None
        assert result["error"] == "API Error"
    
    @patch('utils.response_formatter.client')
    def test_format_response_api_error_no_meta(self, mock_client):
        """Test format_response when API call fails without meta."""
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        result = format_response("Test prompt", return_meta=False)
        
        assert result is None
    
    @patch('utils.response_formatter.client', None)
    def test_format_response_no_client(self):
        """Test format_response when OpenAI client is not available."""
        result = format_response("Test prompt", return_meta=True)
        
        assert isinstance(result, dict)
        assert result["raw_response"] is None
        assert result["parsed_response"] is None
        assert "OpenAI client not initialized" in result["error"]
    
    @patch('utils.response_formatter.client', None)
    def test_format_response_no_client_no_meta(self):
        """Test format_response when OpenAI client is not available without meta."""
        result = format_response("Test prompt", return_meta=False)
        
        assert result is None
