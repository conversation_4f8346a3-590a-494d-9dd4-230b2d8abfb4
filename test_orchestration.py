# test_orchestration.py

from core.agent_builder import agent_executor
from utils.toolkit import ConfigUtils

print("🔧 Loading Config...")
config = ConfigUtils.load_config()

# Ensure model is valid (dynamic validation)
valid_models = ["gpt-4o-mini", "gpt-4o", "gpt-4", "gpt-3.5-turbo"]
current_model = config.get("llm", {}).get("model", "gpt-4o-mini")
if current_model not in valid_models:
    print(f"⚠️ Invalid model '{current_model}', using 'gpt-4o-mini'")
    config["llm"]["model"] = "gpt-4o-mini"

# Test dynamic configuration
print(f"📋 Loaded config with model: {config.get('llm', {}).get('model')}")
print(f"📋 Context limit: {config.get('context_limit')}")
print(f"📋 Default user ID: {config.get('default_user_id')}")
print(f"📋 Context storage path: {config.get('context_storage', {}).get('file_path')}")
print(f"📋 Number of tools configured: {len(config.get('tools', []))}")

# Test 1: General question (should not use tools) - using default user_id from config
print("\n" + "="*50)
print("TEST 1: General Question (Default User)")
print("="*50)
prompt1 = "What is the capital of France?"
print(f"🚀 Query: {prompt1}")

result1 = agent_executor(prompt1, config=config)  # No user_id specified, should use config default

if result1:
    print(f"🎯 Selected Tool: {result1['selected_tool']}")
    print(f"🧠 Reasoning: {result1['reasoning']}")
    print(f"💬 Final Response: {result1['final_response']}")
    print(f"📦 Model Used: {result1['used_model']}")
    if result1.get('error'):
        print(f"❌ Error: {result1['error']}")
else:
    print("❌ Agent execution failed. No response was returned.")

# Test 2: Search query (should use search tool) - with custom user_id
print("\n" + "="*50)
print("TEST 2: Search Query (Custom User)")
print("="*50)
prompt2 = "Search for information about Python programming tutorials"
print(f"🚀 Query: {prompt2}")

result2 = agent_executor(prompt2, config=config, user_id="custom_user_123")

if result2:
    print(f"🎯 Selected Tool: {result2['selected_tool']}")
    print(f"🔧 Tool Parameters: {result2['tool_parameters']}")
    print(f"🧠 Reasoning: {result2['reasoning']}")
    print(f"🛠️ Tool Result: {result2['tool_result']}")
    print(f"💬 Final Response: {result2['final_response']}")
    print(f"📦 Model Used: {result2['used_model']}")
    if result2.get('error'):
        print(f"❌ Error: {result2['error']}")
else:
    print("❌ Agent execution failed. No response was returned.")

# Test 3: Registry query with custom config override
print("\n" + "="*50)
print("TEST 3: Registry Query (Config Override)")
print("="*50)

# Create custom config with different context limit
custom_config = config.copy()
custom_config["context_limit"] = 1  # Only use 1 previous interaction for context
custom_config["default_user_id"] = "admin_user"

prompt3 = "What tools are available?"
print(f"🚀 Query: {prompt3}")
print(f"🔧 Using custom context_limit: {custom_config['context_limit']}")

result3 = agent_executor(prompt3, config=custom_config)  # Will use admin_user as default

if result3:
    print(f"🎯 Selected Tool: {result3['selected_tool']}")
    print(f"🔧 Tool Parameters: {result3['tool_parameters']}")
    print(f"🧠 Reasoning: {result3['reasoning']}")
    print(f"🛠️ Tool Result: {result3['tool_result']}")
    print(f"💬 Final Response: {result3['final_response']}")
    print(f"📦 Model Used: {result3['used_model']}")
    print(f"👤 User ID: {result3['user_id']}")  # Should be admin_user from custom config
    if result3.get('error'):
        print(f"❌ Error: {result3['error']}")
else:
    print("❌ Agent execution failed. No response was returned.")

# Test 4: Fully dynamic configuration with enhanced features
print("\n" + "="*50)
print("TEST 4: Fully Dynamic Configuration with Enhanced Features")
print("="*50)

# Create a completely custom configuration with all dynamic features
fully_custom_config = {
    "llm": {"model": "gpt-4o-mini", "temperature": 0.2},
    "system_prompt": "You are a specialized AI assistant focused on providing concise, accurate information.",
    "default_user_id": "dynamic_test_user",
    "context_limit": 1,
    "enable_parameter_enhancement": True,  # Enable AI parameter enhancement
    "enable_ai_response_generation": True,  # Enable AI-driven tool responses
    "context_storage": {"file_path": "data/custom_history.json"},
    "context_format": {
        "user_role": "human",
        "assistant_role": "ai",
        "include_metadata": True
    },
    "registry_config": {
        "registration_message": "🚀 Dynamically registered: {tool_name}"
    },
    "prompt_templates": {
        "tool_selection": "Analyze this request: {user_input}\n\nAvailable tools:\n{tools_text}\n\nIMPORTANT: Respond with ONLY valid JSON. No additional text.\n\nRequired format:\n{{\"selected_tool\": \"name_or_none\", \"parameters\": {{}}, \"reasoning\": \"explanation\", \"direct_response\": \"answer_if_no_tool\"}}\n\nJSON only:"
    },
    "error_messages": {
        "general_error": "Custom error: {error}"
    },
    "tools": [
        {
            "name": "search_tool",
            "description": "Advanced search with dynamic parameters",
            "execute_func": "core.tools.search_execute",
            "schema": {
                "type": "object",
                "properties": {
                    "query": {"type": "string"},
                    "search_type": {"type": "string", "enum": ["web", "academic", "news"]},
                    "max_results": {"type": "integer"}
                },
                "required": ["query"]
            }
        },
        {
            "name": "registry_tool",
            "description": "Dynamic registry query with custom formatting",
            "execute_func": "core.tools.registry_query_tool_execute",
            "schema": {
                "type": "object",
                "properties": {
                    "output_format": {"type": "string", "enum": ["list", "json", "detailed"]},
                    "include_descriptions": {"type": "boolean"}
                }
            }
        }
    ]
}

prompt4 = "Search for recent academic papers on machine learning"
print(f"🚀 Query: {prompt4}")
print(f"🔧 Using enhanced config with parameter enhancement: {fully_custom_config['enable_parameter_enhancement']}")
print(f"🔧 Custom context format: {fully_custom_config['context_format']}")

result4 = agent_executor(prompt4, config=fully_custom_config)

if result4:
    print(f"🎯 Selected Tool: {result4['selected_tool']}")
    print(f"🧠 Reasoning: {result4['reasoning']}")
    print(f"💬 Final Response: {result4['final_response']}")
    print(f"👤 User ID: {result4['user_id']}")  # Should be dynamic_test_user
    if result4.get('error'):
        print(f"❌ Error: {result4['error']}")
else:
    print("❌ Agent execution failed. No response was returned.")
