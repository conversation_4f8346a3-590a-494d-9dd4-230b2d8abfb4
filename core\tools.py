from typing import Callable, Dict, Any

class Tool:
    def __init__(self, name: str, description: str, execute_func: Callable, schema: Dict[str, Any]):
        self.name = name
        self.description = description
        self.execute = execute_func
        self.schema = schema



def search_execute(params: Dict[str, Any]) -> str:
    """Fully dynamic search tool with AI-driven response generation."""
    query = params.get("query", "")
    search_type = params.get("search_type", "web")
    max_results = params.get("max_results", 5)
    config = params.get("config", {})

    # AI-driven response template generation
    if config.get("enable_ai_response_generation", False):
        from utils.response_formatter import format_response

        template_prompt = f"""Generate a realistic search result response for:
Query: {query}
Search Type: {search_type}
Max Results: {max_results}

Create a helpful, informative response that simulates what a {search_type} search would return. Be specific and relevant to the query."""

        try:
            ai_response = format_response(
                prompt=template_prompt,
                formatter="answer",
                model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
                temperature=0.7
            )
            if ai_response:
                return ai_response
        except Exception as e:
            # Fallback to template-based response
            pass

    # Fallback: Template-based response
    response_template = params.get("response_template", "Search results for: {query}")

    # Dynamic response modifiers
    modifiers = {
        "academic": f" (Academic search, {max_results} scholarly results)",
        "news": f" (Latest news, {max_results} articles)",
        "web": f" ({max_results} web results)"
    }

    base_response = response_template.format(query=query)
    modifier = modifiers.get(search_type, f" ({max_results} results)")

    return base_response + modifier

def registry_query_tool_execute(params: Dict[str, Any]) -> str:
    """AI-driven registry query tool with intelligent response generation."""
    registry = params.get("registry")
    output_format = params.get("output_format", "list")
    include_descriptions = params.get("include_descriptions", False)
    config = params.get("config", {})
    original_query = params.get("original_query", "")

    if not registry:
        no_registry_msg = params.get("no_registry_message", "Tool registry not available.")
        return no_registry_msg

    tool_names = registry.list_tools()

    # AI-driven response generation
    if config.get("enable_ai_response_generation", False) and original_query:
        from utils.response_formatter import format_response

        # Build tool information for AI
        tools_info = []
        for name in tool_names:
            tool = registry.get_tool(name)
            if tool:
                tools_info.append(f"- {name}: {tool.description}")
            else:
                tools_info.append(f"- {name}")

        tools_text = "\n".join(tools_info)

        ai_prompt = f"""The user asked: "{original_query}"

Available tools:
{tools_text}

Provide a helpful response about the available tools. Be conversational and explain how these tools could help the user. Format: {output_format}"""

        try:
            ai_response = format_response(
                prompt=ai_prompt,
                formatter="answer",
                model_name=config.get("llm", {}).get("model", "gpt-4o-mini"),
                temperature=0.7
            )
            if ai_response:
                return ai_response
        except Exception as e:
            # Fallback to template-based response
            pass

    # Fallback: Template-based response
    if output_format == "json":
        tools_info = []
        for name in tool_names:
            tool = registry.get_tool(name)
            tool_info = {"name": name}
            if include_descriptions and tool:
                tool_info["description"] = tool.description
            tools_info.append(tool_info)
        return str(tools_info)
    elif output_format == "detailed":
        result = "Available tools:\n"
        for name in tool_names:
            tool = registry.get_tool(name)
            result += f"- {name}"
            if include_descriptions and tool:
                result += f": {tool.description}"
            result += "\n"
        return result.strip()
    else:
        # Default list format
        list_template = params.get("list_template", "Available tools: {tools}")
        return list_template.format(tools=', '.join(tool_names))
