"""
Tests for core.agent_registry module.
"""

import pytest
from core.agent_registry import ToolRegistry
from core.tools import Tool


class TestToolRegistry:
    """Test the ToolRegistry class."""
    
    def test_registry_creation(self):
        """Test creating a ToolRegistry instance."""
        registry = ToolRegistry()
        assert registry._tools == {}
        assert registry.config == {}
        assert registry.registration_callbacks == []
    
    def test_registry_creation_with_config(self):
        """Test creating a ToolRegistry with configuration."""
        config = {"registration_message": "Custom: {tool_name}"}
        registry = ToolRegistry(config)
        assert registry.config == config
    
    def test_register_tool(self):
        """Test registering a tool."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool(
            name="test_tool",
            description="Test tool",
            execute_func=dummy_execute,
            schema={}
        )
        
        registry.register_tool(tool)
        assert "test_tool" in registry._tools
        assert registry._tools["test_tool"] == tool
    
    def test_get_tool(self):
        """Test getting a tool by name."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool(
            name="test_tool",
            description="Test tool",
            execute_func=dummy_execute,
            schema={}
        )
        
        registry.register_tool(tool)
        retrieved_tool = registry.get_tool("test_tool")
        assert retrieved_tool == tool
    
    def test_get_nonexistent_tool(self):
        """Test getting a tool that doesn't exist."""
        registry = ToolRegistry()
        result = registry.get_tool("nonexistent")
        assert result is None
    
    def test_get_tools(self):
        """Test getting all registered tools."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        tool1 = Tool("tool1", "First tool", dummy_execute, {})
        tool2 = Tool("tool2", "Second tool", dummy_execute, {})
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        
        tools = registry.get_tools()
        assert len(tools) == 2
        assert tool1 in tools
        assert tool2 in tools
    
    def test_list_tools(self):
        """Test listing tool names."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        tool1 = Tool("tool1", "First tool", dummy_execute, {})
        tool2 = Tool("tool2", "Second tool", dummy_execute, {})
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        
        tool_names = registry.list_tools()
        assert len(tool_names) == 2
        assert "tool1" in tool_names
        assert "tool2" in tool_names
    
    def test_get_tools_by_category(self):
        """Test getting tools by category."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        # Create tools with categories
        tool1 = Tool("tool1", "First tool", dummy_execute, {})
        tool1.category = "search"
        
        tool2 = Tool("tool2", "Second tool", dummy_execute, {})
        tool2.category = "utility"
        
        tool3 = Tool("tool3", "Third tool", dummy_execute, {})
        tool3.category = "search"
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        registry.register_tool(tool3)
        
        search_tools = registry.get_tools_by_category("search")
        assert len(search_tools) == 2
        assert tool1 in search_tools
        assert tool3 in search_tools
        assert tool2 not in search_tools
        
        utility_tools = registry.get_tools_by_category("utility")
        assert len(utility_tools) == 1
        assert tool2 in utility_tools
    
    def test_search_tools(self):
        """Test searching tools by name or description."""
        registry = ToolRegistry()
        
        def dummy_execute(params):
            return "result"
        
        tool1 = Tool("search_tool", "Tool for searching", dummy_execute, {})
        tool2 = Tool("calc_tool", "Calculator utility", dummy_execute, {})
        tool3 = Tool("web_search", "Web search functionality", dummy_execute, {})
        
        registry.register_tool(tool1)
        registry.register_tool(tool2)
        registry.register_tool(tool3)
        
        # Search by name
        search_results = registry.search_tools("search")
        assert len(search_results) == 2
        assert tool1 in search_results
        assert tool3 in search_results
        
        # Search by description
        calc_results = registry.search_tools("calculator")
        assert len(calc_results) == 1
        assert tool2 in calc_results
        
        # Case insensitive search
        web_results = registry.search_tools("WEB")
        assert len(web_results) == 1
        assert tool3 in web_results
    
    def test_registration_callbacks(self):
        """Test registration callbacks."""
        registry = ToolRegistry()
        callback_called = []
        
        def test_callback(tool):
            callback_called.append(tool.name)
        
        registry.add_registration_callback(test_callback)
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "Test tool", dummy_execute, {})
        registry.register_tool(tool)
        
        assert len(callback_called) == 1
        assert callback_called[0] == "test_tool"
    
    def test_registration_callback_error_handling(self):
        """Test that callback errors don't break registration."""
        registry = ToolRegistry()
        
        def failing_callback(tool):
            raise Exception("Callback failed")
        
        registry.add_registration_callback(failing_callback)
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "Test tool", dummy_execute, {})
        
        # Should not raise exception despite callback failure
        registry.register_tool(tool)
        assert registry.get_tool("test_tool") == tool
    
    def test_multiple_callbacks(self):
        """Test multiple registration callbacks."""
        registry = ToolRegistry()
        callback1_called = []
        callback2_called = []
        
        def callback1(tool):
            callback1_called.append(tool.name)
        
        def callback2(tool):
            callback2_called.append(tool.name)
        
        registry.add_registration_callback(callback1)
        registry.add_registration_callback(callback2)
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "Test tool", dummy_execute, {})
        registry.register_tool(tool)
        
        assert callback1_called == ["test_tool"]
        assert callback2_called == ["test_tool"]
