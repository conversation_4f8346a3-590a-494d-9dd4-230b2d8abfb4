"""
Tests for core.agent_builder module.
"""

import pytest
import json
from unittest.mock import Mo<PERSON>, patch, MagicMock
from core.agent_builder import (
    agent_executor,
    _build_tool_selection_prompt,
    _extract_tool_decision,
    _enhance_tool_parameters
)
from core.tools import Tool
from core.agent_registry import ToolRegistry


class TestBuildToolSelectionPrompt:
    """Test the _build_tool_selection_prompt function."""
    
    def test_build_prompt_basic(self):
        """Test building a basic tool selection prompt."""
        def dummy_execute(params):
            return "result"
        
        tools = [
            Tool("test_tool", "A test tool", dummy_execute, {"type": "object"})
        ]
        
        prompt = _build_tool_selection_prompt("Test query", tools)
        
        assert "Test query" in prompt
        assert "test_tool" in prompt
        assert "A test tool" in prompt
        assert "JSON" in prompt
        assert "selected_tool" in prompt
    
    def test_build_prompt_with_context(self):
        """Test building prompt with context messages."""
        def dummy_execute(params):
            return "result"
        
        tools = [Tool("test_tool", "A test tool", dummy_execute, {})]
        context_messages = [
            {"role": "user", "content": "Previous question"},
            {"role": "assistant", "content": "Previous answer"}
        ]
        
        prompt = _build_tool_selection_prompt("Test query", tools, context_messages)
        
        assert "Previous conversation context" in prompt
    
    def test_build_prompt_with_custom_config(self):
        """Test building prompt with custom configuration."""
        def dummy_execute(params):
            return "result"
        
        tools = [Tool("test_tool", "A test tool", dummy_execute, {})]
        config = {
            "prompt_templates": {
                "tool_selection": "Custom template: {user_input} | Tools: {tools_text}"
            }
        }
        
        prompt = _build_tool_selection_prompt("Test query", tools, config=config)
        
        assert "Custom template:" in prompt
        assert "Test query" in prompt


class TestExtractToolDecision:
    """Test the _extract_tool_decision function."""
    
    def test_extract_valid_json(self):
        """Test extracting a valid JSON decision."""
        response = '{"selected_tool": "test_tool", "parameters": {"query": "test"}, "reasoning": "test reason", "direct_response": ""}'
        
        result = _extract_tool_decision(response)
        
        assert result["selected_tool"] == "test_tool"
        assert result["parameters"]["query"] == "test"
        assert result["reasoning"] == "test reason"
    
    def test_extract_json_with_code_blocks(self):
        """Test extracting JSON wrapped in code blocks."""
        response = '```json\n{"selected_tool": "test_tool", "parameters": {}, "reasoning": "test", "direct_response": ""}\n```'
        
        result = _extract_tool_decision(response)
        
        assert result["selected_tool"] == "test_tool"
    
    def test_extract_malformed_json(self):
        """Test extracting from malformed JSON."""
        response = 'Some text with "selected_tool": "test_tool" and "reasoning": "test reason"'
        
        result = _extract_tool_decision(response)
        
        assert result["selected_tool"] == "test_tool"
        assert result["reasoning"] == "test reason"
    
    def test_extract_no_json(self):
        """Test extracting when no JSON is found."""
        response = "This is just plain text with no JSON structure."
        
        result = _extract_tool_decision(response)
        
        assert result["selected_tool"] == "none"
        assert result["direct_response"] == response
        assert "Could not parse" in result["reasoning"]
    
    def test_extract_empty_response(self):
        """Test extracting from empty response."""
        result = _extract_tool_decision("")
        
        assert result["selected_tool"] == "none"
        assert "Empty response" in result["reasoning"]
    
    def test_extract_with_custom_config(self):
        """Test extracting with custom configuration patterns."""
        config = {
            "json_extraction": {
                "patterns": ["\\{.*?selected_tool.*?\\}"]
            }
        }
        
        response = 'Here is the decision: {"selected_tool": "custom_tool", "parameters": {}}'
        
        result = _extract_tool_decision(response, config)
        
        assert result["selected_tool"] == "custom_tool"


class TestEnhanceToolParameters:
    """Test the _enhance_tool_parameters function."""
    
    @patch('core.agent_builder.format_response')
    def test_enhance_parameters_success(self, mock_format_response):
        """Test successful parameter enhancement."""
        mock_format_response.return_value = {
            "parsed_response": {"query": "enhanced query", "max_results": 10},
            "error": None
        }
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "A test tool", dummy_execute, {})
        config = {"enable_parameter_enhancement": True}
        params = {"query": "original query"}
        
        result = _enhance_tool_parameters(params, tool, config)
        
        assert result["query"] == "enhanced query"
        assert result["max_results"] == 10
    
    @patch('core.agent_builder.format_response')
    def test_enhance_parameters_failure(self, mock_format_response):
        """Test parameter enhancement failure fallback."""
        mock_format_response.side_effect = Exception("Enhancement failed")
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "A test tool", dummy_execute, {})
        config = {"enable_parameter_enhancement": True}
        params = {"query": "original query"}
        
        result = _enhance_tool_parameters(params, tool, config)
        
        # Should return original parameters on failure
        assert result == params
    
    @patch('core.agent_builder.format_response')
    def test_enhance_parameters_invalid_response(self, mock_format_response):
        """Test parameter enhancement with invalid response."""
        mock_format_response.return_value = {
            "parsed_response": "not a dict",
            "error": None
        }
        
        def dummy_execute(params):
            return "result"
        
        tool = Tool("test_tool", "A test tool", dummy_execute, {})
        config = {"enable_parameter_enhancement": True}
        params = {"query": "original query"}
        
        result = _enhance_tool_parameters(params, tool, config)
        
        # Should return original parameters when response is not a dict
        assert result == params


class TestAgentExecutor:
    """Test the main agent_executor function."""

    @patch('core.agent_builder.format_response')
    @patch('core.agent_builder.ContextManager')
    def test_agent_executor_direct_response(self, mock_context_manager, mock_format_response, sample_config):
        """Test agent executor with direct response (no tool needed)."""
        # Mock context manager
        mock_cm_instance = Mock()
        mock_cm_instance.get_recent_messages.return_value = []
        mock_context_manager.return_value = mock_cm_instance

        # Mock LLM response for tool selection (direct response)
        mock_format_response.return_value = {
            "raw_response": '{"selected_tool": "none", "parameters": {}, "reasoning": "Direct answer", "direct_response": "Paris is the capital of France"}',
            "parsed_response": {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": "Direct answer",
                "direct_response": "Paris is the capital of France"
            },
            "error": None
        }

        result = agent_executor("What is the capital of France?", config=sample_config)

        assert result["selected_tool"] == "none"
        assert result["final_response"] == "Paris is the capital of France"
        assert result["reasoning"] == "Direct answer"
        assert "error" not in result

    @patch('core.agent_builder.format_response')
    @patch('core.agent_builder.ContextManager')
    def test_agent_executor_tool_execution(self, mock_context_manager, mock_format_response, sample_config):
        """Test agent executor with tool execution."""
        # Mock context manager
        mock_cm_instance = Mock()
        mock_cm_instance.get_recent_messages.return_value = []
        mock_context_manager.return_value = mock_cm_instance

        # Mock LLM responses
        mock_format_response.side_effect = [
            # Tool selection response
            {
                "raw_response": '{"selected_tool": "test_tool", "parameters": {"query": "test query"}, "reasoning": "Using test tool", "direct_response": ""}',
                "parsed_response": {
                    "selected_tool": "test_tool",
                    "parameters": {"query": "test query"},
                    "reasoning": "Using test tool",
                    "direct_response": ""
                },
                "error": None
            },
            # Final response generation
            {
                "parsed_response": "Here are the test results based on your query.",
                "error": None
            }
        ]

        # Mock tool execution
        with patch('tests.test_tools.mock_tool_execute') as mock_tool_exec:
            mock_tool_exec.return_value = "Mock tool result"

            result = agent_executor("Test query", config=sample_config)

        assert result["selected_tool"] == "test_tool"
        assert result["tool_parameters"]["query"] == "test query"
        assert result["reasoning"] == "Using test tool"
        assert result["final_response"] == "Here are the test results based on your query."

    @patch('core.agent_builder.format_response')
    @patch('core.agent_builder.ContextManager')
    def test_agent_executor_tool_not_found(self, mock_context_manager, mock_format_response, sample_config):
        """Test agent executor when selected tool is not found."""
        # Mock context manager
        mock_cm_instance = Mock()
        mock_cm_instance.get_recent_messages.return_value = []
        mock_context_manager.return_value = mock_cm_instance

        # Mock LLM response selecting non-existent tool
        mock_format_response.return_value = {
            "raw_response": '{"selected_tool": "nonexistent_tool", "parameters": {}, "reasoning": "Using nonexistent tool", "direct_response": "Fallback response"}',
            "parsed_response": {
                "selected_tool": "nonexistent_tool",
                "parameters": {},
                "reasoning": "Using nonexistent tool",
                "direct_response": "Fallback response"
            },
            "error": None
        }

        result = agent_executor("Test query", config=sample_config)

        assert result["selected_tool"] == "nonexistent_tool"
        assert "not available" in result["final_response"]
        assert "Fallback response" in result["final_response"]

    @patch('core.agent_builder.format_response')
    @patch('core.agent_builder.ContextManager')
    def test_agent_executor_llm_error(self, mock_context_manager, mock_format_response, sample_config):
        """Test agent executor when LLM call fails."""
        # Mock context manager
        mock_cm_instance = Mock()
        mock_cm_instance.get_recent_messages.return_value = []
        mock_context_manager.return_value = mock_cm_instance

        # Mock LLM error
        mock_format_response.return_value = {
            "raw_response": None,
            "parsed_response": None,
            "error": "API rate limit exceeded"
        }

        result = agent_executor("Test query", config=sample_config)

        assert result["selected_tool"] == "none"
        assert "technical difficulties" in result["final_response"]

    @patch('core.agent_builder.format_response')
    @patch('core.agent_builder.ContextManager')
    def test_agent_executor_with_context(self, mock_context_manager, mock_format_response, sample_config):
        """Test agent executor with conversation context."""
        # Mock context manager with previous messages
        mock_cm_instance = Mock()
        mock_cm_instance.get_recent_messages.return_value = [
            {"role": "user", "content": "Previous question"},
            {"role": "assistant", "content": "Previous answer"}
        ]
        mock_context_manager.return_value = mock_cm_instance

        # Mock LLM response
        mock_format_response.return_value = {
            "raw_response": '{"selected_tool": "none", "parameters": {}, "reasoning": "Direct answer", "direct_response": "Based on our previous conversation..."}',
            "parsed_response": {
                "selected_tool": "none",
                "parameters": {},
                "reasoning": "Direct answer",
                "direct_response": "Based on our previous conversation..."
            },
            "error": None
        }

        result = agent_executor("Follow-up question", config=sample_config, user_id="test_user")

        # Verify context was requested
        mock_cm_instance.get_recent_messages.assert_called_with("test_user", limit=3, format_config=sample_config["context_format"])

        # Verify context was passed to LLM
        call_args = mock_format_response.call_args
        assert call_args[1]["messages"] == [
            {"role": "user", "content": "Previous question"},
            {"role": "assistant", "content": "Previous answer"}
        ]
