# ai_utility_orchestrator/utils/toolkit.py

import os, json, logging, importlib
from typing import Dict, List, Callable, Any
from pathlib import Path

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

try:
    from openai import OpenAI
except ImportError:
    OpenAI = None

logger = logging.getLogger("ai_utility_orchestrator.toolkit")

class ConfigUtils:
    @staticmethod
    def load_config(file_path: str = None, overrides: dict = None, user_id: str = None) -> Dict[str, Any]:
        # Try to find config file in multiple locations
        if file_path:
            config_path = file_path
        else:
            # Look for config in current directory, then parent directories
            current_dir = Path.cwd()
            possible_paths = [
                current_dir / "config" / "config_default.json",
                current_dir / "config_default.json",
                current_dir.parent / "config" / "config_default.json",
            ]

            config_path = None
            for path in possible_paths:
                if path.exists():
                    config_path = str(path)
                    break

            if not config_path:
                config_path = os.getenv("AI_ORCHESTRATOR_CONFIG", "config/config_default.json")

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
        except FileNotFoundError:
            logger.warning(f"Config file not found: {config_path}. Using minimal default config.")
            config = {
                "llm": {"model": "gpt-4o-mini", "temperature": 0.7},
                "system_prompt": "You are an AI Orchestrator that helps users by routing queries to appropriate tools.",
                "default_user_id": "default_user",
                "context_limit": 3,
                "enable_parameter_enhancement": False,
                "enable_ai_response_generation": False,
                "context_storage": {"backend": "file", "file_path": "data/chat_history.json"},
                "context_format": {"user_role": "user", "assistant_role": "assistant", "include_metadata": False},
                "registry_config": {"registration_message": "✅ Registered tool: {tool_name}"},
                "tools": [],
                "output_format": "answer",
                "error_messages": {
                    "tool_execution_failed": "I tried to use the {tool_name} tool, but encountered an error: {error}",
                    "tool_not_found": "I wanted to use the {tool_name} tool, but it's not available. Let me try to help you directly: {fallback_response}",
                    "general_error": "I apologize, but I encountered an error while processing your request: {error}"
                },
                "default_responses": {
                    "no_tool_needed": "I can help you with that, but I'm not sure how to respond."
                }
            }

        # Optional: apply overrides and user config
        if user_id:
            user_config_path = Path(config_path).parent / f"user_{user_id}.json"
            if user_config_path.exists():
                with open(user_config_path, "r", encoding="utf-8") as f:
                    config.update(json.load(f))

        if overrides:
            config.update(overrides)

        return config

    @staticmethod
    def load_tools_from_config(config: Dict[str, Any]) -> List[Dict[str, Any]]:
        return config.get("tools", [])

class EnvUtils:
    @staticmethod
    def get_env(key: str, default: str = "", required: bool = False) -> str:
        value = os.getenv(key, default)
        if required and not value:
            raise EnvironmentError(f"Missing required environment variable: {key}")
        return value

class LLMUtils:
    model_name = "gpt-4o-mini"
    temperature = 0.7

    @classmethod
    def set_model(cls, model, temp):
        cls.model_name = model
        cls.temperature = temp

    @classmethod
    def generate_response(cls, prompt: str) -> str:
        # Simulated LLM call (you can plug your real OpenAI or other LLM API here)
        return f"[{cls.model_name}@{cls.temperature}] → {prompt}"


class DynamicImportUtils:
    @staticmethod
    def load_object(path: str) -> Any:
        try:
            module_path, attr = path.rsplit(".", 1)
            module = importlib.import_module(module_path)
            return getattr(module, attr)
        except Exception as e:
            logger.error(f"Could not import {path}: {e}")
            return None

class RegistryUtils:
    registered_tools: Dict[str, Callable] = {}

    @classmethod
    def register_tool(cls, name: str):
        def wrapper(func: Callable):
            cls.registered_tools[name] = func
            logger.info(f"Registered tool: {name}")
            return func
        return wrapper

    @classmethod
    def get_tool(cls, name: str) -> Callable:
        return cls.registered_tools.get(name)

    @classmethod
    def list_registered_tools(cls) -> List[str]:
        return list(cls.registered_tools.keys())
