"""
Tests for core.tools module.
"""

import pytest
from core.tools import Tool, search_execute, registry_query_tool_execute
from core.agent_registry import ToolRegistry


def mock_tool_execute(params):
    """Mock tool execution function for testing."""
    query = params.get("query", "")
    return f"Mock execution result for: {query}"


class TestTool:
    """Test the Tool class."""
    
    def test_tool_creation(self):
        """Test creating a Tool instance."""
        def dummy_execute(params):
            return "dummy result"
        
        tool = Tool(
            name="test_tool",
            description="A test tool",
            execute_func=dummy_execute,
            schema={"type": "object"}
        )
        
        assert tool.name == "test_tool"
        assert tool.description == "A test tool"
        assert tool.schema == {"type": "object"}
        assert tool.execute({"test": "param"}) == "dummy result"
    
    def test_tool_execution(self):
        """Test tool execution with parameters."""
        def echo_execute(params):
            return f"Echo: {params.get('message', 'no message')}"
        
        tool = Tool(
            name="echo_tool",
            description="Echoes input",
            execute_func=echo_execute,
            schema={}
        )
        
        result = tool.execute({"message": "hello world"})
        assert result == "Echo: hello world"


class TestSearchExecute:
    """Test the search_execute function."""
    
    def test_basic_search(self):
        """Test basic search functionality."""
        params = {
            "query": "Python programming",
            "search_type": "web",
            "max_results": 5
        }
        
        result = search_execute(params)
        assert "Python programming" in result
        assert "5 web results" in result
    
    def test_academic_search(self):
        """Test academic search type."""
        params = {
            "query": "machine learning",
            "search_type": "academic",
            "max_results": 3
        }
        
        result = search_execute(params)
        assert "machine learning" in result
        assert "3 scholarly results" in result
    
    def test_news_search(self):
        """Test news search type."""
        params = {
            "query": "AI developments",
            "search_type": "news",
            "max_results": 10
        }
        
        result = search_execute(params)
        assert "AI developments" in result
        assert "10 articles" in result
    
    def test_custom_response_template(self):
        """Test custom response template."""
        params = {
            "query": "test query",
            "response_template": "Custom search for: {query}"
        }
        
        result = search_execute(params)
        assert "Custom search for: test query" in result
    
    @pytest.mark.parametrize("search_type,expected", [
        ("web", "web results"),
        ("academic", "scholarly results"),
        ("news", "articles"),
        ("unknown", "results")
    ])
    def test_search_type_modifiers(self, search_type, expected):
        """Test different search type modifiers."""
        params = {
            "query": "test",
            "search_type": search_type,
            "max_results": 1
        }
        
        result = search_execute(params)
        assert expected in result


class TestRegistryQueryTool:
    """Test the registry_query_tool_execute function."""
    
    def test_registry_query_without_registry(self):
        """Test registry query when no registry is provided."""
        params = {}
        result = registry_query_tool_execute(params)
        assert "Tool registry not available" in result
    
    def test_registry_query_list_format(self, mock_tool_registry):
        """Test registry query with list format."""
        params = {
            "registry": mock_tool_registry,
            "output_format": "list"
        }
        
        result = registry_query_tool_execute(params)
        assert "test_tool" in result
        assert "Available tools:" in result
    
    def test_registry_query_detailed_format(self, mock_tool_registry):
        """Test registry query with detailed format."""
        params = {
            "registry": mock_tool_registry,
            "output_format": "detailed",
            "include_descriptions": True
        }
        
        result = registry_query_tool_execute(params)
        assert "test_tool" in result
        assert "A test tool" in result
        assert "Available tools:" in result
    
    def test_registry_query_json_format(self, mock_tool_registry):
        """Test registry query with JSON format."""
        params = {
            "registry": mock_tool_registry,
            "output_format": "json",
            "include_descriptions": True
        }
        
        result = registry_query_tool_execute(params)
        assert "test_tool" in result
        # Should contain dictionary-like structure as string
        assert "name" in result
    
    def test_custom_list_template(self, mock_tool_registry):
        """Test custom list template."""
        params = {
            "registry": mock_tool_registry,
            "output_format": "list",
            "list_template": "Tools found: {tools}"
        }
        
        result = registry_query_tool_execute(params)
        assert "Tools found:" in result
        assert "test_tool" in result
    
    def test_custom_no_registry_message(self):
        """Test custom no registry message."""
        params = {
            "no_registry_message": "Custom error: No registry available"
        }
        
        result = registry_query_tool_execute(params)
        assert "Custom error: No registry available" in result
