#!/usr/bin/env python3
"""
Test runner script for AI Utility Orchestrator.
Provides convenient commands for running different types of tests.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"\n{'='*60}")
    print(f"🧪 {description}")
    print(f"{'='*60}")
    print(f"Running: {' '.join(cmd)}")
    print()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} failed with exit code {e.returncode}")
        return False
    except FileNotFoundError:
        print(f"\n❌ Command not found: {cmd[0]}")
        print("Make sure pytest is installed: pip install pytest pytest-cov")
        return False


def main():
    parser = argparse.ArgumentParser(description="Test runner for AI Utility Orchestrator")
    parser.add_argument(
        "test_type",
        choices=["all", "unit", "integration", "coverage", "fast", "slow"],
        nargs="?",
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--no-cov",
        action="store_true",
        help="Skip coverage reporting"
    )
    parser.add_argument(
        "--html-cov",
        action="store_true",
        help="Generate HTML coverage report"
    )
    
    args = parser.parse_args()
    
    # Base pytest command
    base_cmd = ["python", "-m", "pytest"]
    
    if args.verbose:
        base_cmd.append("-v")
    
    # Test type specific commands
    commands = []
    
    if args.test_type == "all":
        cmd = base_cmd.copy()
        if not args.no_cov:
            cmd.extend(["--cov=core", "--cov=utils", "--cov-report=term-missing"])
            if args.html_cov:
                cmd.append("--cov-report=html:htmlcov")
        commands.append((cmd, "Running all tests"))
    
    elif args.test_type == "unit":
        cmd = base_cmd + ["-m", "not integration"]
        if not args.no_cov:
            cmd.extend(["--cov=core", "--cov=utils", "--cov-report=term-missing"])
        commands.append((cmd, "Running unit tests"))
    
    elif args.test_type == "integration":
        cmd = base_cmd + ["-m", "integration"]
        commands.append((cmd, "Running integration tests"))
    
    elif args.test_type == "coverage":
        cmd = base_cmd + [
            "--cov=core",
            "--cov=utils",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "--cov-fail-under=80"
        ]
        commands.append((cmd, "Running tests with coverage analysis"))
    
    elif args.test_type == "fast":
        cmd = base_cmd + ["-m", "not slow and not integration"]
        commands.append((cmd, "Running fast tests only"))
    
    elif args.test_type == "slow":
        cmd = base_cmd + ["-m", "slow"]
        commands.append((cmd, "Running slow tests only"))
    
    # Run commands
    success = True
    for cmd, description in commands:
        if not run_command(cmd, description):
            success = False
            break
    
    # Summary
    print(f"\n{'='*60}")
    if success:
        print("🎉 All tests completed successfully!")
        if args.html_cov and not args.no_cov:
            print(f"📊 HTML coverage report generated: {Path.cwd() / 'htmlcov' / 'index.html'}")
    else:
        print("💥 Some tests failed!")
        sys.exit(1)
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
